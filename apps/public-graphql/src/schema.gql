# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

directive @upper on FIELD_DEFINITION

type NotificationType {
  id: String!
  categoryCode: String!
  categoryName: String!
  description: String
  createdAt: DateTime!
  updatedAt: DateTime!
  deletedAt: DateTime!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type UserEmbeddedWallet {
  id: String!
  chain: ChainType!
  walletAddress: String!
  walletId: String!
  walletAccountId: String!
  hdPath: String!
  name: String!
}

"""Supported blockchain types"""
enum ChainType {
  EVM
  SOLANA
  TRON
  ARB
}

type User {
  id: String!
  authProvider: AuthProvider!
  walletAddress: String
  googleId: String
  email: String
  avatar: String
  isExportedWallet: Boolean!
  isFirstLogin: Boolean!
  userManagedWallets: [UserManagedWalletDTO!]!
  userEmbeddedWallets: [UserEmbeddedWalletDTO!]!
}

"""Supported provider for authentication"""
enum AuthProvider {
  TELEGRAM
  CHAIN_EVM
  CHAIN_SOL
  CHAIN_TRON
  GOOGLE
  EMAIL
}

type InitOtpAuthResponseDto {
  """OTP ID for verification"""
  otpId: String!

  """User ID in the system"""
  userId: String!

  """Sub-organization ID"""
  subOrgId: String!

  """Lifetime of otp in seconds"""
  ttl: Float!
}

type ApprovedPassphraseResponse {
  activityId: String!
  passphrase: String!
}

type ApprovedPrivateKeyResponse {
  activityId: String!
  privateKey: String!
}

type ApprovedCreateWalletResponse {
  wallet: UserEmbeddedWallet!
}

type MarkedAsExportedPassphraseResponse {
  updated: Boolean!
}

type LoginDTO {
  accessToken: String!
  refreshToken: String!
  userId: String!
  referrerCode: String
  fingerprint: String
}

type RefreshAccessTokenDTO {
  accessToken: String!
}

type SubOrgResponseDTO {
  subOrgId: String!
  userId: String!
}

type LoginV2DTO {
  accessToken: String!
  refreshToken: String!
  userId: String!
  referrerCode: String
  fingerprint: String
  subOrgId: String!
  turnKeyResponse: String!
}

type TurnKeyCredentialResponseDto {
  session: String!
}

type LoginGoogleDTO {
  accessToken: String!
  refreshToken: String!
  userId: String!
  referrerCode: String
  fingerprint: String
  subOrgId: String!
  turnKeyResponse: TurnKeyCredentialResponseDto!
}

type TurnKeyLogEmailOtpResponseDto {
  session: String!
}

type LoginEmailOtpDTO {
  accessToken: String!
  refreshToken: String!
  userId: String!
  referrerCode: String
  fingerprint: String
  subOrgId: String!
  turnKeyResponse: TurnKeyLogEmailOtpResponseDto!
}

type UserManagedWalletDTO {
  id: String!
  chain: ChainType!
  walletAddress: String!
  balance: Float!
}

type UserEmbeddedWalletDTO {
  id: String!
  chain: ChainType!
  walletAddress: String!
  walletId: String!
  walletAccountId: String!
  hdPath: String!
  name: String!
  balance: Float!
}

type UpdateEmbeddedWalletNameResponseDTO {
  """Whether the update was successful"""
  success: Boolean!

  """Success or error message"""
  message: String!

  """Updated wallet information"""
  wallet: UserEmbeddedWallet
}

type PerpetualStatusDTO {
  approvedAgent: Boolean!
  setReferral: Boolean!
  setFeeBuilder: Boolean!
  agent: String
  agentName: String!
  feeBuilderAddress: String!
  feeBuilderPercent: Float!
  referralCode: String!
}

type SignatureType {
  r: String!
  s: String!
  v: Int!
}

type SignedCreateOrderDTO {
  signature: SignatureType!
  userId: String!
}

type SignedCancelOrderDTO {
  signature: SignatureType!
  userId: String!
}

"""
Signature of Hyperliquid transaction that uses to submit to Hyperliquid API
"""
type SignatureDTO {
  signature: SignatureType!
  userId: String!
}

type TwoFactorDTO {
  userId: String!
  isEnabled: Boolean!
}

type SetupNew2FAResponse {
  secretCode: String!
  recoveryCodes: [String!]!
  uri: String!
}

type UserNotificationPreferenceDTO {
  id: String!
  userId: String!
  notificationTypeCode: String!
  channel: String!
  isEnabled: Boolean!
}

type UserWithdrawalWhitelistAddressDTO {
  id: String!
  userId: String!
  address: String!
  nickname: String
}

type UserSettingsDTO {
  id: String!
  notificationPreferences: [UserNotificationPreferenceDTO!]!
  withdrawalWhitelistAddresses: [UserWithdrawalWhitelistAddressDTO!]!
  googleAuthenticator: TwoFactorDTO
}

"""scalar for decimal value"""
scalar DecimalScalar

type Query {
  getNonce(wallAddress: String!): String!
  verifyTOTP(code: String!): Boolean!
  account: User!
  userSettings: UserSettingsDTO!
  setupNew2FA: SetupNew2FAResponse!
  verify2FA(code: String!): Boolean!
}

type Mutation {
  loginByWallet(message: String!, signature: String!, chainType: ChainType!, isOkxWallet: Boolean! = false, referrerCode: String, fingerprint: String): LoginDTO!
  loginByTelegram(userId: String!, code: String!, referrerCode: String, fingerprint: String): LoginDTO!
  getAccessToken(refreshToken: String!): RefreshAccessTokenDTO!
  createWalletSubOrg(input: GetWalletSubOrgInputDTO!): SubOrgResponseDTO!
  loginWithGoogle(input: GetGoogleSubOrgInputDTO!): LoginGoogleDTO!
  loginByWalletV2(input: InputLoginWalletV2Dto!): LoginV2DTO!
  createEmailSubOrg(input: GetEmailSubOrgInputDTO!): SubOrgResponseDTO!
  initEmailOtp(input: InitEmailOtpInputDTO!): InitOtpAuthResponseDto!
  loginWithEmailOtp(input: LoginWithEmailOtpInputDTO!): LoginEmailOtpDTO!
  checkHyperLiquidWallet: PerpetualStatusDTO!
  updateHyperLiquidWallet(input: InputPerpetualStatusDTO!): PerpetualStatusDTO!
  signHyperLiquidCreateOrder(input: InputSignCreateOrderDTO!): SignedCreateOrderDTO!
  signHyperLiquidCancelOrder(input: InputSignCancelOrderDTO!): SignedCancelOrderDTO!
  signHyperLiquidUpdateLeverage(input: InputSignUpdateLeverageDTO!): SignedCancelOrderDTO!
  approveHyperLiquidApproveAgent(input: InputSignApproveAgentDTO!): SignatureDTO!
  approveHyperLiquidFeeBuilder(input: InputSignApproveFeeBuilderDTO!): SignatureDTO!
  approveHyperLiquidReferral(input: InputSignApproveReferralDTO!): SignatureDTO!
  approveExportPassphrase(input: ExportPassphraseInput!): ApprovedPassphraseResponse!
  approveExportPrivateKey(input: ExportPrivateKeyInput!): ApprovedPrivateKeyResponse!
  markedAsExportedPassphrase: MarkedAsExportedPassphraseResponse!
  requestReverifyOtp(input: RequestReverifyOtpInputDto!): InitOtpAuthResponseDto!
  updateEmbeddedWalletName(input: UpdateEmbeddedWalletNameInputDTO!): UpdateEmbeddedWalletNameResponseDTO!
  approveCreateWallet(input: ApproveCreateWalletInput!): ApprovedCreateWalletResponse!
  updatePreference(input: UpdatePreferenceInput!): Boolean!
  disable2FA(otpCode: String!): Boolean!
}

input GetWalletSubOrgInputDTO {
  """Request signing message"""
  message: String!

  """Signature of message"""
  signature: String!

  """Using okx wallet to signing"""
  isOkxWallet: Boolean! = false
  chainType: ChainType!
  referrerCode: String
  fingerprint: String
}

input GetGoogleSubOrgInputDTO {
  idToken: String!
  fingerprint: String
  referrerCode: String

  """Target public key"""
  targetPublicKey: String!
}

input InputLoginWalletV2Dto {
  """Sub Organization ID"""
  organizationId: String!

  """Public key to address Turnkey session"""
  publicKey: String!

  """Session expire time in seconds"""
  expirationSeconds: String!

  """Request timestamp in milliseconds"""
  timestampMs: String!

  """Stamp header name"""
  stampHeaderName: String!

  """Stamp header value"""
  stampHeaderValue: String!

  """URL for the API call"""
  url: String!
}

input GetEmailSubOrgInputDTO {
  """Email address"""
  email: String!
  referrerCode: String
  fingerprint: String
}

input InitEmailOtpInputDTO {
  """Email address"""
  email: String!
  referrerCode: String
  fingerprint: String
}

input LoginWithEmailOtpInputDTO {
  """Email address"""
  email: String!

  """OTP ID from initialization"""
  otpId: String!

  """OTP code received by user"""
  otpCode: String!

  """Target public key for credential encryption"""
  targetPublicKey: String!
}

input InputPerpetualStatusDTO {
  agentExpiredAt: Float
  setReferral: Boolean!
  setFeeBuilder: Boolean!
  feeBuilderAddress: String!
  feeBuilderPercent: Float!
  referralCode: String!
}

input InputSignCreateOrderDTO {
  action: OrderRequestInput!
  nonce: Float!
  vaultAddress: String
}

input OrderRequestInput {
  type: String!
  orders: [OrderInput!]!
  grouping: String
  builder: BuilderInput
}

input OrderInput {
  a: Float!
  b: Boolean!
  p: String!
  s: String!
  r: Boolean!
  t: OrderTypeInput!
  c: String
}

input OrderTypeInput {
  limit: LimitInput
  trigger: TriggerInput
}

input LimitInput {
  tif: String!
}

input TriggerInput {
  isMarket: Boolean!
  triggerPx: String!
  tpsl: String!
}

input BuilderInput {
  b: String!
  f: Float!
}

input InputSignCancelOrderDTO {
  action: CancelActionInput!
  nonce: Float!
  vaultAddress: String
}

input CancelActionInput {
  type: String!
  cancels: [CancelInput!]!
}

input CancelInput {
  a: Int!
  o: Float!
}

input InputSignUpdateLeverageDTO {
  action: UpdateLeverageActionInput!
  nonce: Float!
  vaultAddress: String
}

input UpdateLeverageActionInput {
  type: String!
  asset: Int!
  isCross: Boolean!
  leverage: Int!
}

input InputSignApproveAgentDTO {
  """
  The address of the agent wallet that needs to approve. BE uses to validate transaction
  """
  agentAddress: String!

  """
  The nonce of transaction that was built from FE. BE uses to validate transaction
  """
  nonce: Float!

  """
  The name of the agent wallet that needs to approve. BE uses to validate transaction
  """
  agentName: String!

  """
  The ID of the activity that submitted to Turnkey from FE thru signRawPayload methods
  """
  activityId: String!
}

"""Note that builder address needs to matched with config in BE"""
input InputSignApproveFeeBuilderDTO {
  """
  The nonce of transaction that was built from FE. BE uses to validate transaction
  """
  nonce: Float!

  """
  The ID of the activity that submitted to Turnkey from FE thru signRawPayload methods
  """
  activityId: String!
}

"""Note that referral code needs to matched with config in BE"""
input InputSignApproveReferralDTO {
  """
  The nonce of transaction that was built from FE. BE uses to validate transaction
  """
  nonce: Float!

  """
  The ID of the activity that submitted to Turnkey from FE thru signRawPayload methods
  """
  activityId: String!
}

input ExportPassphraseInput {
  """Signature of signing request if user login by 3rd wallet"""
  signature: String

  """Message was signed by user"""
  message: String

  """Is user login by OKX wallet"""
  isOkxWallet: Boolean

  """OIDC token for user who is authenticated by Google"""
  oidcToken: String

  """OTP ID for user who is authenticated by email OTP"""
  otpId: String

  """OTP code for user who is authenticated by email OTP"""
  otpCode: String
  publicKey: String!
  activityId: String!
}

input ExportPrivateKeyInput {
  """Signature of signing request if user login by 3rd wallet"""
  signature: String

  """Message was signed by user"""
  message: String

  """Is user login by OKX wallet"""
  isOkxWallet: Boolean

  """OIDC token for user who is authenticated by Google"""
  oidcToken: String

  """OTP ID for user who is authenticated by email OTP"""
  otpId: String

  """OTP code for user who is authenticated by email OTP"""
  otpCode: String
  publicKey: String!
  activityId: String!
}

input RequestReverifyOtpInputDto {
  fingerprint: String
}

input UpdateEmbeddedWalletNameInputDTO {
  """User Embedded Wallet ID"""
  id: String!

  """New wallet name"""
  name: String!
}

input ApproveCreateWalletInput {
  activityId: String!

  """New wallet name, max length is 127"""
  name: String!
}

input UpdatePreferenceInput {
  notificationTypeCode: NotificationTypeCategoryCode!
  isEnabled: Boolean!
}

"""Notification type category code"""
enum NotificationTypeCategoryCode {
  SmartMoneyActivity
  PriceChange
  FuturesSignal
}